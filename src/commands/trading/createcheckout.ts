import {
    ActionRowBuilder,
    ApplicationCommandOptionType,
    ApplicationCommandType,
    Client,
    CommandInteraction,
    StringSelectMenuBuilder
} from "discord.js";
import { Command } from "../../types";
import pool from "../../utils/dbConfig";

export const createcheckout: Command = {
    name: "createcheckout",
    description: "Create a checkout/ticket for a specific user",
    category: "trading",
    type: ApplicationCommandType.ChatInput,
    options: [
        {
            name: "username",
            description: "The username to create checkout for (from Wikily profiles)",
            type: ApplicationCommandOptionType.String,
            required: true
        },
        {
            name: "discord_user",
            description: "The Discord user to create the cart channel for",
            type: ApplicationCommandOptionType.User,
            required: true
        }
    ],
    run: async (_client: Client, interaction: CommandInteraction) => {
        await interaction.deferReply({ ephemeral: true });

        const username = interaction.options.get("username")?.value as string;
        const discordUser = interaction.options.get("discord_user")?.user;

        if (!username) {
            await interaction.editReply({ content: "Please provide a valid username." });
            return;
        }

        if (!discordUser) {
            await interaction.editReply({ content: "Please provide a valid Discord user." });
            return;
        }

        try {
            // Look up the user ID from the profiles table
            const profileQuery = `
                SELECT id, username
                FROM public.profiles
                WHERE LOWER(username) = LOWER($1)
            `;

            const profileResult = await pool.query(profileQuery, [username]);

            if (profileResult.rows.length === 0) {
                await interaction.editReply({
                    content: `❌ No user found with username: **${username}**\n\nPlease make sure the username is spelled correctly and exists in the Wikily database.`
                });
                return;
            }

            const userProfile = profileResult.rows[0];
            const userId = userProfile.id; // This is the UUID from the profiles table

            // Create category selection menu
            const categorySelect = new StringSelectMenuBuilder()
                .setCustomId(`checkout_category_${userId}_${discordUser.id}`)
                .setPlaceholder('Select a category to browse listings')
                .addOptions([
                    {
                        label: 'Items',
                        value: 'item',
                        emoji: '🎒',
                        description: 'General items and resources'
                    },
                    {
                        label: 'Dinosaurs',
                        value: 'dino',
                        emoji: '🦕',
                        description: 'Tamed dinosaurs and creatures'
                    },
                    {
                        label: 'Eggs',
                        value: 'egg',
                        emoji: '🥚',
                        description: 'Fertilized eggs for breeding'
                    },
                    {
                        label: 'Blueprints',
                        value: 'blueprint',
                        emoji: '📋',
                        description: 'Item blueprints and schematics'
                    },
                    {
                        label: 'Boss Fights',
                        value: 'boss_fight',
                        emoji: '⚔️',
                        description: 'Boss fight services and carries'
                    },
                    {
                        label: 'Base Spots',
                        value: 'base_spot',
                        emoji: '🏠',
                        description: 'Base locations and spots'
                    },
                    {
                        label: 'XP Parties',
                        value: 'xp_party',
                        emoji: '🎉',
                        description: 'Experience farming parties'
                    },
                    {
                        label: 'Other',
                        value: 'other',
                        emoji: '❓',
                        description: 'Miscellaneous services and items'
                    },
                    {
                        label: 'Bundles',
                        value: 'bundle',
                        emoji: '📦',
                        description: 'Item bundles and packages'
                    }
                ]);

            const categoryRow = new ActionRowBuilder<StringSelectMenuBuilder>()
                .addComponents(categorySelect);

            await interaction.editReply({
                content: `🎫 **Create Checkout for User:** **${userProfile.username}** → **${discordUser.username}**\n\nSelect a category to browse **${userProfile.username}**'s listings for **${discordUser}**:`,
                components: [categoryRow]
            });

        } catch (error) {
            console.error('Error in createcheckout command:', error);
            await interaction.editReply({
                content: 'An error occurred while processing your request. Please try again later.'
            });
        }
    }
};
