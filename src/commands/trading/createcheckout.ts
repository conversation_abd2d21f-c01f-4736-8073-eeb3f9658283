import { 
    ActionRowBuilder, 
    ApplicationCommandOptionType, 
    ApplicationCommandType, 
    Client, 
    CommandInteraction, 
    StringSelectMenuBuilder 
} from "discord.js";
import { Command } from "../../types";

export const createcheckout: Command = {
    name: "createcheckout",
    description: "Create a checkout/ticket for a specific user",
    category: "trading",
    type: ApplicationCommandType.ChatInput,
    options: [
        {
            name: "user_id",
            description: "The Discord user ID to create checkout for",
            type: ApplicationCommandOptionType.String,
            required: true
        }
    ],
    run: async (_client: Client, interaction: CommandInteraction) => {
        await interaction.deferReply({ ephemeral: true });

        const userId = interaction.options.get("user_id")?.value as string;

        if (!userId) {
            await interaction.editReply({ content: "Please provide a valid user ID." });
            return;
        }

        try {
            // Validate that the user ID is a valid Discord user ID format
            if (!/^\d{17,19}$/.test(userId)) {
                await interaction.editReply({ 
                    content: "Invalid user ID format. Please provide a valid Discord user ID (17-19 digits)." 
                });
                return;
            }

            // Create category selection menu
            const categorySelect = new StringSelectMenuBuilder()
                .setCustomId(`checkout_category_${userId}`)
                .setPlaceholder('Select a category to browse listings')
                .addOptions([
                    {
                        label: 'Items',
                        value: 'item',
                        emoji: '🎒',
                        description: 'General items and resources'
                    },
                    {
                        label: 'Dinosaurs',
                        value: 'dino',
                        emoji: '🦕',
                        description: 'Tamed dinosaurs and creatures'
                    },
                    {
                        label: 'Eggs',
                        value: 'egg',
                        emoji: '🥚',
                        description: 'Fertilized eggs for breeding'
                    },
                    {
                        label: 'Blueprints',
                        value: 'blueprint',
                        emoji: '📋',
                        description: 'Item blueprints and schematics'
                    },
                    {
                        label: 'Boss Fights',
                        value: 'boss_fight',
                        emoji: '⚔️',
                        description: 'Boss fight services and carries'
                    },
                    {
                        label: 'Base Spots',
                        value: 'base_spot',
                        emoji: '🏠',
                        description: 'Base locations and spots'
                    },
                    {
                        label: 'XP Parties',
                        value: 'xp_party',
                        emoji: '🎉',
                        description: 'Experience farming parties'
                    },
                    {
                        label: 'Other',
                        value: 'other',
                        emoji: '❓',
                        description: 'Miscellaneous services and items'
                    },
                    {
                        label: 'Bundles',
                        value: 'bundle',
                        emoji: '📦',
                        description: 'Item bundles and packages'
                    }
                ]);

            const categoryRow = new ActionRowBuilder<StringSelectMenuBuilder>()
                .addComponents(categorySelect);

            await interaction.editReply({
                content: `🎫 **Create Checkout for User:** <@${userId}>\n\nSelect a category to browse their listings:`,
                components: [categoryRow]
            });

        } catch (error) {
            console.error('Error in createcheckout command:', error);
            await interaction.editReply({
                content: 'An error occurred while processing your request. Please try again later.'
            });
        }
    }
};
