import {
    ActionRowBuilder,
    StringSelectMenuBuilder,
    StringSelectMenuInteraction,
    EmbedBuilder
} from "discord.js";
import pool from "../../utils/dbConfig";
import { EmojiService } from "../../services/emoji-service";
import { NumberFormatter } from "../../utils/numberFormatter";

/**
 * Handle shop listing selection for permanent shop embeds
 */
export async function handleShopListingSelection(
    interaction: StringSelectMenuInteraction,
    userId: string,
    interactingUserId?: string
): Promise<void> {
    await interaction.deferUpdate();

    const selectedValue = interaction.values[0];

    try {
        // Get user profile information
        const profileQuery = `
            SELECT username 
            FROM public.profiles 
            WHERE id = $1
        `;

        const profileResult = await pool.query(profileQuery, [userId]);
        const username = profileResult.rows.length > 0 ? profileResult.rows[0].username : 'Unknown User';

        if (selectedValue === 'back_to_categories') {
            // Return to main shop view
            await returnToMainShop(interaction, userId, username, interactingUserId);
        } else if (selectedValue === 'no_listings') {
            await interaction.followUp({
                content: 'No listings available in this category.',
                ephemeral: true
            });
        } else {
            // Handle specific listing selection - show listing details and purchase options
            await showListingDetails(interaction, selectedValue, userId, username, interactingUserId);
        }

    } catch (error) {
        console.error('Error in shop listing selection handler:', error);
        await interaction.followUp({
            content: 'An error occurred while processing your selection. Please try again later.',
            ephemeral: true
        });
    }
}

/**
 * Return to the main shop category view
 */
async function returnToMainShop(
    interaction: StringSelectMenuInteraction,
    userId: string,
    username: string,
    interactingUserId?: string
): Promise<void> {
    // Check how many listings this user has
    const listingCountQuery = `
        SELECT COUNT(*) as count
        FROM asa.listings 
        WHERE user_id = $1 
        AND (availability IS NULL OR availability != 'not-available')
    `;

    const countResult = await pool.query(listingCountQuery, [userId]);
    const listingCount = parseInt(countResult.rows[0].count);

    // Create the main shop embed
    const shopEmbed = new EmbedBuilder()
        .setTitle(`🛒 ${username}'s Shop`)
        .setDescription(`Browse and purchase items from **${username}**'s collection.\n\n📦 **${listingCount}** listings available\n\nSelect a category below to get started!`)
        .setColor('#00ff88')
        .setTimestamp()
        .setFooter({ 
            text: 'This shop is always available • Select a category to browse',
            iconURL: interaction.client.user?.displayAvatarURL()
        });

    // Create category selection menu (ephemeral for this user)
    const categorySelect = new StringSelectMenuBuilder()
        .setCustomId(`shop_category_${userId}_${interactingUserId || 'unknown'}`)
        .setPlaceholder('🏪 Select a category to browse listings')
        .addOptions([
            {
                label: 'Items',
                value: 'item',
                emoji: '🎒',
                description: 'General items and resources'
            },
            {
                label: 'Dinosaurs',
                value: 'dino',
                emoji: '🦕',
                description: 'Tamed dinosaurs and creatures'
            },
            {
                label: 'Eggs',
                value: 'egg',
                emoji: '🥚',
                description: 'Fertilized eggs for breeding'
            },
            {
                label: 'Blueprints',
                value: 'blueprint',
                emoji: '📋',
                description: 'Item blueprints and schematics'
            },
            {
                label: 'Boss Fights',
                value: 'boss_fight',
                emoji: '⚔️',
                description: 'Boss fight services and carries'
            },
            {
                label: 'Base Spots',
                value: 'base_spot',
                emoji: '🏠',
                description: 'Base locations and spots'
            },
            {
                label: 'XP Parties',
                value: 'xp_party',
                emoji: '🎉',
                description: 'Experience farming parties'
            },
            {
                label: 'Other',
                value: 'other',
                emoji: '❓',
                description: 'Miscellaneous services and items'
            },
            {
                label: 'Bundles',
                value: 'bundle',
                emoji: '📦',
                description: 'Item bundles and packages'
            }
        ]);

    const categoryRow = new ActionRowBuilder<StringSelectMenuBuilder>()
        .addComponents(categorySelect);

    await interaction.editReply({
        embeds: [shopEmbed],
        components: [categoryRow]
    });
}

/**
 * Show listing details and purchase options
 */
async function showListingDetails(
    interaction: StringSelectMenuInteraction,
    listingId: string,
    _userId: string,
    username: string,
    interactingUserId?: string
): Promise<void> {
    // Get the listing information
    const listingQuery = `
        SELECT l.*
        FROM asa.listings l
        WHERE l.listing_id = $1
    `;

    const listingResult = await pool.query(listingQuery, [listingId]);

    if (listingResult.rows.length === 0) {
        await interaction.followUp({
            content: 'This listing no longer exists.',
            ephemeral: true
        });
        return;
    }

    const listing = listingResult.rows[0];
    const isBuyingListing = listing.listing_type === 'buy';

    // Fetch payment options for this listing
    const paymentQuery = `
        SELECT p.*, i.name as item_name
        FROM asa.payment_options p
        LEFT JOIN asa.items i ON SPLIT_PART(p.item_id, ' ', 1) = SPLIT_PART(i.id, ' ', 1)
        WHERE p.listing_id = $1
    `;

    const paymentResult = await pool.query(paymentQuery, [listingId]);

    // Create payment selection menu with contextual placeholder
    const placeholder = isBuyingListing
        ? 'Select what you want to receive'
        : 'Select payment method and add to cart';

    const paymentSelect = new StringSelectMenuBuilder()
        .setCustomId(`shop_payment_${listingId}_${interactingUserId || 'unknown'}`)
        .setPlaceholder(placeholder);

    // Add "Back to Listings" as the first option
    paymentSelect.addOptions({
        label: '← Back to Listings',
        value: 'back_to_listings',
        emoji: '🔙',
        description: 'Return to listing selection'
    });

    if (paymentResult.rows.length > 0) {
        for (const payment of paymentResult.rows) {
            let paymentLabel = '';
            let emoji = ':hlna:';

            if (payment.item_name) {
                const formattedQuantity = NumberFormatter.formatWithCommas(payment.quantity);
                paymentLabel = `${payment.item_name} x${formattedQuantity}`;
                emoji = EmojiService.getItemEmoji(payment.item_name);
            } else if (payment.custom_price) {
                paymentLabel = payment.custom_price;
                emoji = EmojiService.getCurrencyEmoji(payment.custom_price);
            }

            paymentSelect.addOptions({
                label: paymentLabel.substring(0, 100),
                value: payment.payment_id.toString(),
                emoji: emoji
            });
        }
    } else {
        // Add a default option if no payment methods are found
        const contactLabel = isBuyingListing
            ? 'Contact buyer for details'
            : 'Contact seller for payment details';

        paymentSelect.addOptions({
            label: contactLabel,
            value: 'contact',
            emoji: '📞'
        });
    }

    const paymentRow = new ActionRowBuilder<StringSelectMenuBuilder>()
        .addComponents(paymentSelect);

    // Create listing details embed
    const listingEmbed = new EmbedBuilder()
        .setTitle(`${isBuyingListing ? '💰' : '🛒'} ${listing.title || `Listing #${listingId}`}`)
        .setDescription(`**Seller:** ${username}\n**Type:** ${isBuyingListing ? 'Buying' : 'Selling'}\n**Category:** ${listing.category}\n\n${listing.description || 'No description provided.'}`)
        .setColor(isBuyingListing ? '#ff6b6b' : '#00ff88')
        .setTimestamp()
        .setFooter({ 
            text: 'Select a payment option to proceed • Use "Back to Listings" to return',
            iconURL: interaction.client.user?.displayAvatarURL()
        });

    await interaction.editReply({
        embeds: [listingEmbed],
        components: [paymentRow]
    });
}
