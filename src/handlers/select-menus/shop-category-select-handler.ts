import {
    ActionRowBuilder,
    StringSelectMenuBuilder,
    StringSelectMenuInteraction,
    EmbedBuilder
} from "discord.js";
import pool from "../../utils/dbConfig";
import { ChannelService } from "../../services/channel-service";
import { CartSummaryService } from "../../services/cart-summary-service";
import { GuildRoleHelper } from "../../utils/guildRoleHelper";

/**
 * Handle shop category selection for permanent shop embeds
 */
export async function handleShopCategorySelection(
    interaction: StringSelectMenuInteraction,
    userId: string
): Promise<void> {
    await interaction.deferUpdate();

    const selectedCategory = interaction.values[0];

    try {
        // Get user profile information
        const profileQuery = `
            SELECT username
            FROM public.profiles
            WHERE id = $1
        `;

        const profileResult = await pool.query(profileQuery, [userId]);
        const username = profileResult.rows.length > 0 ? profileResult.rows[0].username : 'Unknown User';

        // Handle "Create Ticket" option
        if (selectedCategory === 'create_ticket') {
            await handleCreateTicket(interaction, userId, username);
            return;
        }

        // Query listings for the selected category and user
        const listingsQuery = `
            SELECT listing_id, title, listing_type, category, availability
            FROM asa.listings 
            WHERE user_id = $1 
            AND category = $2 
            AND (availability IS NULL OR availability != 'not-available')
            ORDER BY created_at DESC
            LIMIT 25
        `;

        const listingsResult = await pool.query(listingsQuery, [userId, selectedCategory]);

        // Create listing selection menu
        const listingSelect = new StringSelectMenuBuilder()
            .setCustomId(`shop_listing_${userId}`)
            .setPlaceholder('Select a listing to view details and purchase');

        // Add "Back to Categories" as the first option
        listingSelect.addOptions({
            label: '← Back to Categories',
            value: 'back_to_categories',
            emoji: '🔙',
            description: 'Return to category selection'
        });

        if (listingsResult.rows.length > 0) {
            // Add listings as options
            for (const listing of listingsResult.rows) {
                const listingTitle = listing.title || `Listing #${listing.listing_id}`;
                const typeEmoji = listing.listing_type === 'buy' ? '💰' : '🛒';
                const availabilityText = listing.availability === 'soon' ? ' (Soon)' : 
                                       listing.availability === 'unofficial' ? ' (Unofficial)' : '';
                
                listingSelect.addOptions({
                    label: `${listingTitle}${availabilityText}`.substring(0, 100),
                    value: listing.listing_id.toString(),
                    emoji: typeEmoji,
                    description: `${listing.listing_type === 'buy' ? 'Buying' : 'Selling'} - ${selectedCategory}`.substring(0, 100)
                });
            }
        }

        // If no listings found, show appropriate message
        if (listingsResult.rows.length === 0) {
            listingSelect.addOptions({
                label: 'No listings found in this category',
                value: 'no_listings',
                emoji: '❌',
                description: 'This user has no available listings in this category'
            });
        }

        const listingRow = new ActionRowBuilder<StringSelectMenuBuilder>()
            .addComponents(listingSelect);

        const categoryEmojis: { [key: string]: string } = {
            'item': '🎒',
            'dino': '🦕',
            'egg': '🥚',
            'blueprint': '📋',
            'boss_fight': '⚔️',
            'base_spot': '🏠',
            'xp_party': '🎉',
            'other': '❓',
            'bundle': '📦'
        };

        const categoryName = selectedCategory.charAt(0).toUpperCase() + selectedCategory.slice(1).replace('_', ' ');
        const emoji = categoryEmojis[selectedCategory] || '📋';

        // Create updated embed for the category view
        const categoryEmbed = new EmbedBuilder()
            .setTitle(`${emoji} ${categoryName} - ${username}'s Shop`)
            .setDescription(`Browse **${username}**'s ${categoryName.toLowerCase()} listings.\n\n📦 **${listingsResult.rows.length}** listing(s) found\n\nSelect a listing below to view details and purchase options.`)
            .setColor('#00ff88')
            .setTimestamp()
            .setFooter({ 
                text: 'Select a listing to view details • Use "Back to Categories" to return',
                iconURL: interaction.client.user?.displayAvatarURL()
            });

        await interaction.editReply({
            embeds: [categoryEmbed],
            components: [listingRow]
        });

    } catch (error) {
        console.error('Error in shop category selection handler:', error);
        await interaction.followUp({
            content: 'An error occurred while fetching listings. Please try again later.',
            ephemeral: true
        });
    }
}

/**
 * Handle "Create Ticket" option from shop
 */
async function handleCreateTicket(
    interaction: StringSelectMenuInteraction,
    _userId: string,
    username: string
): Promise<void> {
    if (!interaction.guild) {
        await interaction.followUp({
            content: 'This feature can only be used in a server.',
            ephemeral: true
        });
        return;
    }

    try {
        // Create cart channel for the user who clicked
        const { channel, isNew: isNewChannel } = await ChannelService.getOrCreateCartChannel(
            interaction.guild,
            interaction.client,
            interaction.user.id,
            interaction.user.username
        );

        if (!channel) {
            await interaction.followUp({
                content: 'Error: Could not create or access ticket channel.',
                ephemeral: true
            });
            return;
        }

        if (isNewChannel) {
            // Get cart notify roles to tag
            const notifyRoles = await GuildRoleHelper.getGuildRoles(interaction.guild.id, 'cart_notify');

            // Create notification content for ticket
            let notificationContent = `🎫 **Ticket Created**\n${interaction.user}, this is your personal ticket channel! You can discuss trades and ask questions here.\n\n**Shop:** **${username}**'s listings\n**Created from:** Shop interface`;

            // Add role notifications if any are configured
            if (notifyRoles.length > 0) {
                const roleMentions = notifyRoles.map(roleId => `<@&${roleId}>`).join(' ');
                notificationContent += `\n\n🔔 **New Ticket Alert:** ${roleMentions}`;
            }

            // Send initial message with user tag for identification
            await channel.send({
                content: notificationContent
            });
        } else {
            // Channel already exists, just notify about the new conversation
            await channel.send({
                content: `🎫 **New Conversation**\n${interaction.user} has started a new conversation about **${username}**'s shop.`
            });
        }

        // Update the cart summary (even for empty tickets)
        await CartSummaryService.updateCartSummary(
            channel,
            interaction.user.id,
            interaction.guild.id,
            interaction.user.toString()
        );

        // Notify the user
        const notificationMessage = isNewChannel
            ? `Ticket created! Your ticket channel is ${channel}.`
            : `Conversation started in your existing ticket channel ${channel}.`;

        await interaction.followUp({
            content: notificationMessage,
            ephemeral: true
        });

    } catch (error) {
        console.error('Error creating ticket from shop:', error);
        await interaction.followUp({
            content: 'Error creating ticket. Please try again later.',
            ephemeral: true
        });
    }
}
