import {
    ActionRow<PERSON>uilder,
    StringSelectMenuBuilder,
    StringSelectMenuInteraction
} from "discord.js";
import pool from "../../utils/dbConfig";
import { ChannelService } from "../../services/channel-service";
import { CartSummaryService } from "../../services/cart-summary-service";
import { EmojiService } from "../../services/emoji-service";
import { NumberFormatter } from "../../utils/numberFormatter";
import { GuildRoleHelper } from "../../utils/guildRoleHelper";

/**
 * Handle listing selection for checkout creation
 */
export async function handleListingSelection(
    interaction: StringSelectMenuInteraction,
    targetUserId: string
): Promise<void> {
    await interaction.deferUpdate();

    const selectedValue = interaction.values[0];

    try {
        if (selectedValue === 'discuss_ticket') {
            // Create a general discussion ticket (cart channel)
            await createDiscussionTicket(interaction, targetUserId);
        } else if (selectedValue === 'no_listings') {
            await interaction.followUp({
                content: 'No listings available in this category.',
                ephemeral: true
            });
        } else {
            // Handle specific listing selection - show payment options
            await showPaymentOptions(interaction, selectedValue, targetUserId);
        }

    } catch (error) {
        console.error('Error in listing selection handler:', error);
        await interaction.followUp({
            content: 'An error occurred while processing your selection. Please try again later.',
            ephemeral: true
        });
    }
}

/**
 * Create a general discussion ticket (cart channel)
 */
async function createDiscussionTicket(
    interaction: StringSelectMenuInteraction,
    targetUserId: string
): Promise<void> {
    if (!interaction.guild) {
        await interaction.followUp({
            content: 'This command can only be used in a server.',
            ephemeral: true
        });
        return;
    }

    try {
        // Get user profile information
        const profileQuery = `
            SELECT username
            FROM public.profiles
            WHERE id = $1
        `;

        const profileResult = await pool.query(profileQuery, [targetUserId]);
        const username = profileResult.rows.length > 0 ? profileResult.rows[0].username : 'Unknown User';

        // Try to get Discord user information (this might fail if user is not in Discord)
        let targetUser;
        try {
            targetUser = await interaction.client.users.fetch(targetUserId);
        } catch (error) {
            // If we can't fetch Discord user, we'll use the username from profiles
            console.log(`Could not fetch Discord user for ${targetUserId}, using profile username: ${username}`);
        }
        
        // Create cart channel for the target user
        const channelUsername = targetUser ? targetUser.username : username;
        const { channel, isNew: isNewChannel } = await ChannelService.getOrCreateCartChannel(
            interaction.guild,
            interaction.client,
            targetUserId,
            channelUsername
        );

        if (!channel) {
            await interaction.followUp({
                content: 'Error: Could not create or access cart channel.',
                ephemeral: true
            });
            return;
        }

        if (isNewChannel) {
            // Get cart notify roles to tag
            const notifyRoles = await GuildRoleHelper.getGuildRoles(interaction.guild.id, 'cart_notify');

            // Create notification content for discussion ticket
            const userMention = targetUser ? targetUser.toString() : `**${username}**`;
            let notificationContent = `💬 **Discussion Ticket Created**\n${userMention}, this is your discussion ticket! You can discuss trades and services here.\n\n**Created by:** ${interaction.user}`;

            // Add role notifications if any are configured
            if (notifyRoles.length > 0) {
                const roleMentions = notifyRoles.map(roleId => `<@&${roleId}>`).join(' ');
                notificationContent += `\n\n🔔 **New Ticket Alert:** ${roleMentions}`;
            }

            // Send initial message with user tag for identification
            await channel.send({
                content: notificationContent
            });
        } else {
            // Channel already exists, just notify about the discussion
            const userMention = targetUser ? targetUser.toString() : `**${username}**`;
            await channel.send({
                content: `💬 **Discussion Continued**\n${interaction.user} has initiated a discussion with ${userMention}.`
            });
        }

        // Update the cart summary (even for empty discussion tickets)
        const userMention = targetUser ? targetUser.toString() : `**${username}**`;
        await CartSummaryService.updateCartSummary(
            channel,
            targetUserId,
            interaction.guild.id,
            userMention
        );

        // Notify the admin who created the ticket
        const displayName = targetUser ? targetUser.username : username;
        const notificationMessage = isNewChannel
            ? `Discussion ticket created for ${displayName} in ${channel}.`
            : `Discussion continued with ${displayName} in existing channel ${channel}.`;

        await interaction.followUp({
            content: notificationMessage,
            ephemeral: true
        });

    } catch (error) {
        console.error('Error creating discussion ticket:', error);
        await interaction.followUp({
            content: 'Error creating discussion ticket. Please try again later.',
            ephemeral: true
        });
    }
}

/**
 * Show payment options for a specific listing
 */
async function showPaymentOptions(
    interaction: StringSelectMenuInteraction,
    listingId: string,
    targetUserId: string
): Promise<void> {
    try {
        // Get the listing information
        const listingQuery = `
            SELECT l.*
            FROM asa.listings l
            WHERE l.listing_id = $1
        `;

        const listingResult = await pool.query(listingQuery, [listingId]);

        if (listingResult.rows.length === 0) {
            await interaction.followUp({
                content: 'This listing no longer exists.',
                ephemeral: true
            });
            return;
        }

        const listing = listingResult.rows[0];
        const isBuyingListing = listing.listing_type === 'buy';

        // Fetch payment options for this listing
        const paymentQuery = `
            SELECT p.*, i.name as item_name
            FROM asa.payment_options p
            LEFT JOIN asa.items i ON SPLIT_PART(p.item_id, ' ', 1) = SPLIT_PART(i.id, ' ', 1)
            WHERE p.listing_id = $1
        `;

        const paymentResult = await pool.query(paymentQuery, [listingId]);

        // Create payment selection menu with contextual placeholder
        const placeholder = isBuyingListing
            ? 'Select what you want to receive'
            : 'Select payment method and quantity';

        const paymentSelect = new StringSelectMenuBuilder()
            .setCustomId(`checkout_payment_${listingId}_${targetUserId}`)
            .setPlaceholder(placeholder);

        if (paymentResult.rows.length > 0) {
            for (const payment of paymentResult.rows) {
                let paymentLabel = '';
                let emoji = ':hlna:';

                if (payment.item_name) {
                    const formattedQuantity = NumberFormatter.formatWithCommas(payment.quantity);
                    paymentLabel = `${payment.item_name} x${formattedQuantity}`;
                    emoji = EmojiService.getItemEmoji(payment.item_name);
                } else if (payment.custom_price) {
                    paymentLabel = payment.custom_price;
                    emoji = EmojiService.getCurrencyEmoji(payment.custom_price);
                }

                paymentSelect.addOptions({
                    label: paymentLabel.substring(0, 100),
                    value: payment.payment_id.toString(),
                    emoji: emoji
                });
            }
        } else {
            // Add a default option if no payment methods are found
            const contactLabel = isBuyingListing
                ? 'Contact buyer for details'
                : 'Contact seller for payment details';

            paymentSelect.addOptions({
                label: contactLabel,
                value: 'contact',
                emoji: '📞'
            });
        }

        const paymentRow = new ActionRowBuilder<StringSelectMenuBuilder>()
            .addComponents(paymentSelect);

        // Send the payment selection menu with contextual message
        const cartMessage = isBuyingListing
            ? '💰 **Sell to Buyer**\nSelect what you want to receive in exchange for selling your items:'
            : '🛒 **Add to Cart**\nSelect payment method and quantity to add this item to your cart:';

        // Get user profile information for display
        const profileQuery = `
            SELECT username
            FROM public.profiles
            WHERE id = $1
        `;

        const profileResult = await pool.query(profileQuery, [targetUserId]);
        const username = profileResult.rows.length > 0 ? profileResult.rows[0].username : 'Unknown User';

        await interaction.editReply({
            content: `${cartMessage}\n\n**Listing:** ${listing.title || `Listing #${listingId}`}\n**Target User:** **${username}**`,
            components: [paymentRow]
        });

    } catch (error) {
        console.error('Error showing payment options:', error);
        await interaction.followUp({
            content: 'Error loading payment options. Please try again later.',
            ephemeral: true
        });
    }
}
