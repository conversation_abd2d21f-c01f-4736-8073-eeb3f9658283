import {
    ModalBuilder,
    StringSelectMenuInteraction,
    TextInputBuilder,
    TextInputStyle,
    ActionRowBuilder
} from "discord.js";
import pool from "../../utils/dbConfig";

/**
 * Handle payment selection for shop purchases
 */
export async function handleShopPaymentSelection(
    interaction: StringSelectMenuInteraction,
    listingId: string
): Promise<void> {
    const paymentId = interaction.values[0];

    try {
        if (paymentId === 'back_to_listings') {
            // Return to listing selection for this category
            await returnToListingSelection(interaction, listingId);
            return;
        }

        // Get listing information to determine type
        const listingQuery = `
            SELECT listing_type, title, user_id
            FROM asa.listings 
            WHERE listing_id = $1
        `;

        const listingResult = await pool.query(listingQuery, [listingId]);

        if (listingResult.rows.length === 0) {
            await interaction.reply({
                content: 'This listing no longer exists.',
                ephemeral: true
            });
            return;
        }

        const listing = listingResult.rows[0];
        const isBuyingListing = listing.listing_type === 'buy';

        // Create and show the quantity modal with contextual text
        const modal = new ModalBuilder()
            .setCustomId(`shop_quantity_${listingId}_${paymentId}`)
            .setTitle(isBuyingListing ? 'Enter Quantity to Sell' : 'Enter Quantity to Buy');

        const quantityLabel = isBuyingListing
            ? 'How many would you like to sell?'
            : 'How many would you like to buy?';

        const quantityInput = new TextInputBuilder()
            .setCustomId('quantity')
            .setLabel(quantityLabel)
            .setStyle(TextInputStyle.Short)
            .setPlaceholder('Enter a number')
            .setRequired(true)
            .setValue('1')
            .setMinLength(1)
            .setMaxLength(7);

        const quantityRow = new ActionRowBuilder<TextInputBuilder>()
            .addComponents(quantityInput);

        modal.addComponents(quantityRow);

        await interaction.showModal(modal);

    } catch (error) {
        console.error('Error in shop payment selection handler:', error);
        await interaction.reply({
            content: 'An error occurred while processing your selection. Please try again later.',
            ephemeral: true
        });
    }
}

/**
 * Return to listing selection for the category
 */
async function returnToListingSelection(
    interaction: StringSelectMenuInteraction,
    listingId: string
): Promise<void> {
    // We need to get the listing's category and user to rebuild the listing selection
    const listingQuery = `
        SELECT category, user_id
        FROM asa.listings 
        WHERE listing_id = $1
    `;

    const listingResult = await pool.query(listingQuery, [listingId]);

    if (listingResult.rows.length === 0) {
        await interaction.reply({
            content: 'Could not return to listings.',
            ephemeral: true
        });
        return;
    }

    const listing = listingResult.rows[0];
    
    // Import and call the shop category handler to rebuild the category view
    const { handleShopCategorySelection } = await import('./shop-category-select-handler.js');
    
    // Temporarily set the interaction values to simulate category selection
    const originalValues = interaction.values;
    interaction.values = [listing.category];
    
    await handleShopCategorySelection(interaction, listing.user_id);
    
    // Restore original values
    interaction.values = originalValues;
}
